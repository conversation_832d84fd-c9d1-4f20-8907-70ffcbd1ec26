{"parsed_sample": [{"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "0.0.0.0/0", "NEXT_HOP": "106.11.72.13", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "65004 65026", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "11.117.47.128/26", "NEXT_HOP": "10.139.103.57", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "4200001092", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "11.139.230.0/24", "NEXT_HOP": "10.139.103.57", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "4200001092", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "11.161.62.113/32", "NEXT_HOP": "10.139.103.57", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "4200001092", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "11.161.62.114/32", "NEXT_HOP": "10.139.103.57", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "4200001092", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "11.161.62.115/32", "NEXT_HOP": "10.139.103.57", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "4200001092", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "11.161.62.116/32", "NEXT_HOP": "10.139.103.57", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "4200001092", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "11.161.62.121/32", "NEXT_HOP": "10.139.103.57", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "4200001092", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "11.161.62.123/32", "NEXT_HOP": "10.139.103.9", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "4200001092", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "11.161.62.124/32", "NEXT_HOP": "10.139.103.17", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "4200001092", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "11.161.62.132/32", "NEXT_HOP": "10.139.103.2", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "11.194.26.64/26", "NEXT_HOP": "10.139.103.57", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "4200001092", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "11.194.158.128/26", "NEXT_HOP": "10.139.103.57", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "4200001092", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "11.194.158.192/26", "NEXT_HOP": "10.139.103.57", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "4200001092", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "21.1.1.1/32", "NEXT_HOP": "106.11.72.13", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "65004 65026", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "21.1.1.2/32", "NEXT_HOP": "106.11.72.13", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "65004 65026", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "21.1.1.3/32", "NEXT_HOP": "106.11.72.13", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "65004 65026", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "21.1.1.4/32", "NEXT_HOP": "106.11.72.13", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "65004 65026", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "21.1.1.5/32", "NEXT_HOP": "106.11.72.13", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "65004 65026", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "21.1.1.6/32", "NEXT_HOP": "106.11.72.13", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "65004 65026", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "21.1.1.7/32", "NEXT_HOP": "106.11.72.13", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "65004 65026", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "21.1.1.8/32", "NEXT_HOP": "106.11.72.13", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "65004 65026", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "21.1.1.9/32", "NEXT_HOP": "106.11.72.13", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "65004 65026", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "21.1.1.10/32", "NEXT_HOP": "106.11.72.13", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "65004 65026", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "21.1.1.11/32", "NEXT_HOP": "106.11.72.13", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "65004 65026", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "21.1.1.12/32", "NEXT_HOP": "106.11.72.13", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "65004 65026", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "21.1.1.13/32", "NEXT_HOP": "106.11.72.13", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "65004 65026", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "21.1.1.14/32", "NEXT_HOP": "106.11.72.13", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "65004 65026", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "21.1.1.15/32", "NEXT_HOP": "106.11.72.13", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "65004 65026", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "21.1.1.16/32", "NEXT_HOP": "106.11.72.13", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "65004 65026", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "21.1.1.17/32", "NEXT_HOP": "106.11.72.13", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "65004 65026", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "21.1.1.18/32", "NEXT_HOP": "106.11.72.13", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "65004 65026", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "21.1.1.19/32", "NEXT_HOP": "106.11.72.13", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "65004 65026", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "21.1.1.20/32", "NEXT_HOP": "106.11.72.13", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "65004 65026", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "21.1.1.21/32", "NEXT_HOP": "106.11.72.13", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "65004 65026", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "21.1.1.22/32", "NEXT_HOP": "106.11.72.13", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "65004 65026", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "21.1.1.23/32", "NEXT_HOP": "106.11.72.13", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "65004 65026", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "21.1.1.24/32", "NEXT_HOP": "106.11.72.13", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "65004 65026", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "21.1.1.25/32", "NEXT_HOP": "106.11.72.13", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "65004 65026", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "21.1.1.26/32", "NEXT_HOP": "106.11.72.13", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "65004 65026", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "21.1.1.27/32", "NEXT_HOP": "106.11.72.13", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "65004 65026", "ORIGIN": "i", "COMMAND_STATUS": ""}]}