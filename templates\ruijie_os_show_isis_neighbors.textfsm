Value SYSTEM_ID (\S+)
Value TYPE (L\d)
Value IP_ADDRESS (\d+.\d+.\d+.\d+)
Value STATE (\S+)
Value HOLD_TIME (\d+)
Value CIRCUIT_ID (\S+)
Value INTERFACE (.+?)

Start
  ^System\s+Id\s+Type\s+IP\s+Address\s+State\s+Holdtime\s+Circuit\s+Id\s+Interface
  ^[-]+\s+[-]+\s+[-]+\s+[-]+\s+[-]+\s+[-]+\s+[-]+\s*$$
  ^${SYSTEM_ID}\s+${TYPE}\s+${IP_ADDRESS}\s+${STATE}\s+${HOLD_TIME}\s+${CIRCUIT_ID}\s+${INTERFACE}\s*$$ -> Record
  ^\s*$$
  ^.
