{"parsed_sample": [{"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "11.117.46.20/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "11.161.62.116/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "11.194.158.192/26", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "11.194.158.194/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "11.194.158.195/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "11.194.158.196/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "11.194.158.197/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.0.0.0/8", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.10/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.11/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.12/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.13/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.14/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.15/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.16/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.17/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.18/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.19/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.20/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.21/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.22/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.23/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.24/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.25/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.26/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.27/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.28/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.29/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.30/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.31/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.32/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.33/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.34/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.35/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.36/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.37/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.38/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.39/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.40/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.41/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.42/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.43/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.44/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.45/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.46/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.47/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.48/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.49/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.50/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.51/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.52/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.53/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.54/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.55/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.56/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.57/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.58/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.59/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.60/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.61/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.62/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.63/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.64/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.65/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.66/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.67/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.68/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.69/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.70/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.71/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.72/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.73/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.74/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.75/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.76/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.77/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.78/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.79/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.80/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.81/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.82/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.83/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.84/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.85/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.86/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.87/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.88/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.89/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.90/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.91/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.92/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.93/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.94/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.95/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.96/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.97/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.98/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.99/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.100/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.101/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.102/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.103/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.104/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.105/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.106/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.107/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.108/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.109/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.110/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.111/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.112/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.113/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.114/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.115/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.116/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.117/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.118/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.119/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.120/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.121/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.122/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.123/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.124/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.125/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.126/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.127/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.128/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.129/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.130/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.131/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.132/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.133/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.134/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.135/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.136/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.137/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.138/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.139/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.140/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.141/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.142/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.143/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.144/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.145/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.146/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.147/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.148/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.149/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.150/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.151/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.152/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.153/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.154/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.155/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.156/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.157/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.158/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.159/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.160/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.161/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.162/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.163/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.164/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.165/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.166/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.167/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.168/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.169/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.170/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.171/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.172/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.173/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.174/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.175/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.176/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.177/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.178/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.179/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.180/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.181/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.182/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.183/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.184/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.185/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.186/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.187/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.188/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.189/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.190/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.191/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.192/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.193/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.194/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.195/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.196/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.197/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.198/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.199/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.200/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.201/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.202/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.203/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.204/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.205/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.206/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.207/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.208/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "178.1.1.209/32", "NEXT_HOP": "11.162.184.14", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "?", "COMMAND_STATUS": ""}]}