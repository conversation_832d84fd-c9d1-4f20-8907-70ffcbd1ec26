{"parsed_sample": [{"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "5.2.1.1/32", "NEXT_HOP": "50.2.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "65021", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.0.0/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.0.64/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.0.128/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.0.192/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.1.0/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.1.64/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.1.128/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.1.192/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.2.0/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.2.64/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.2.128/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.2.192/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.3.0/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.3.64/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.3.128/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.3.192/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.4.0/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.4.64/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.4.128/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.4.192/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.5.0/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.5.64/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.5.128/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.5.192/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.6.0/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.6.64/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.6.128/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.6.192/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.7.0/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.7.64/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.7.128/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.7.192/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.8.0/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.8.64/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "89.0.8.128/26", "NEXT_HOP": "50.11.0.2", "METRIC": "", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "2001 2002", "ORIGIN": "i", "COMMAND_STATUS": ""}]}