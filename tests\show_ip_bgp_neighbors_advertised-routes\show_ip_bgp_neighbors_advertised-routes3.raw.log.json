{"parsed_sample": [{"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "100.1.1.1/32", "NEXT_HOP": "130.200.2.2", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "64086.65006 65001", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "100.1.1.2/32", "NEXT_HOP": "130.200.2.2", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "64086.65006 65001", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "100.1.1.3/32", "NEXT_HOP": "130.200.2.2", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "0", "AS_PATH": "64086.65006", "ORIGIN": "i", "COMMAND_STATUS": ""}, {"STATUS": "*", "PATH_SELECTION": ">", "ROUTE_SOURCE": " ", "NETWORK": "100.1.1.6/32", "NEXT_HOP": "10.200.2.1", "METRIC": "0", "LOCAL_PREF": "", "WEIGHT": "32768", "AS_PATH": "", "ORIGIN": "i", "COMMAND_STATUS": ""}]}